import { Injectable } from '@nestjs/common';
import * as fs from 'fs';
import * as FormData from 'form-data';
import axios from 'axios';

@Injectable()
export class LocalTranscribeService {
  private localTranscribeUrl: string;

  constructor() {
    this.localTranscribeUrl = process.env.LOCAL_TRANSCRIBE_URL || 'http://localhost:8080';
  }

  async transcribeWavFile(filePath: string): Promise<string> {
    try {
      if (!fs.existsSync(filePath)) {
        throw new Error(`File not found: ${filePath}`);
      }

      const formData = new FormData();
      formData.append('file', fs.createReadStream(filePath));
      formData.append('language', 'tr');

      const response = await axios.post(`${this.localTranscribeUrl}/inference`, formData, {
        headers: {
          ...formData.getHeaders(),
        },
        timeout: 30000, // 30 second timeout
      });

      if (response.data && response.data.text) {
        console.log('Local transcription result:', response.data.text);
        return response.data.text;
      } else {
        throw new Error('Invalid response format from local transcription service');
      }
    } catch (error) {
      console.error('Error transcribing audio with local service:', error);
      throw new Error(`Local transcription failed: ${error.message}`);
    }
  }

  async transcribeAudioBuffer(audioBuffer: Buffer, filename: string = 'temp_audio.wav'): Promise<string> {
    const tempFilePath = `uploads/${filename}`;

    try {
      fs.writeFileSync(tempFilePath, audioBuffer);

      const result = await this.transcribeWavFile(tempFilePath);

      fs.unlinkSync(tempFilePath);

      return result;
    } catch (error) {
      if (fs.existsSync(tempFilePath)) {
        fs.unlinkSync(tempFilePath);
      }
      throw error;
    }
  }
}
