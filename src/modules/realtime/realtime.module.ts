import { Modu<PERSON> } from '@nestjs/common';
import { RealtimeController } from './realtimeController';
import { RealtimeService } from './realtime.service';
import { OpenAiRealtimeService } from './openai-realtime.service';
import { AudioService } from './audio.service';
import { WavService } from './wav.service';
import { TranscribeService } from './transcribe.service';
import { LocalTranscribeService } from './local-transcribe.service';

@Module({
  controllers: [RealtimeController],
  providers: [RealtimeService, OpenAiRealtimeService, AudioService, WavService, TranscribeService, LocalTranscribeService],
  exports: [RealtimeService, AudioService, WavService, TranscribeService],
})
export class RealtimeModule {}
