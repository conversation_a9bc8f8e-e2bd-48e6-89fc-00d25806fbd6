import { Injectable } from '@nestjs/common';
import { OpenAI } from 'openai';
import * as fs from 'fs';
import * as process from 'node:process';

@Injectable()
export class TranscribeService {
  private openai: OpenAI;

  constructor() {
    this.openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY,
    });
  }


  async transcribeWavFile(filePath: string): Promise<string> {
    try {
      if (!fs.existsSync(filePath)) {
        throw new Error(`File not found: ${filePath}`);
      }

      const audioFile = fs.createReadStream(filePath);

      const transcription = await this.openai.audio.transcriptions.create({
        file: audioFile,
        model: process.env.OPENAI_API_TRANSCRIBE_MODEL || 'whisper-1',
        language: 'tr',
        response_format: 'text',
      });

      // console.log('Transcription result:', transcription);
      return transcription;
    } catch (error) {
      console.error('Error transcribing audio:', error);
      throw new Error(`Transcription failed: ${error.message}`);
    }
  }

  async transcribeAudioBuffer(audioBuffer: Buffer, filename: string = 'temp_audio.wav'): Promise<string> {
    const tempFilePath = `uploads/${filename}`;

    try {
      fs.writeFileSync(tempFilePath, audioBuffer);

      const result = await this.transcribeWavFile(tempFilePath);

      fs.unlinkSync(tempFilePath);

      return result;
    } catch (error) {
      if (fs.existsSync(tempFilePath)) {
        fs.unlinkSync(tempFilePath);
      }
      throw error;
    }
  }
}
